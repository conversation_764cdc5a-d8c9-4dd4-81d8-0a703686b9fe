<!doctype html>
<html class="__className_aaf875" lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link
      rel="preload"
      href="/_next/static/media/c9a5bc6a7c948fb0-s.p.woff2"
      as="font"
      crossorigin=""
      type="font/woff2"
    />
    <link
      rel="stylesheet"
      href="/_next/static/css/app/layout.css?v=1720419547085"
      data-precedence="next_static/css/app/layout.css"
    />
    <link
      rel="preload"
      as="script"
      fetchpriority="low"
      href="/_next/static/chunks/webpack.js?v=1720419547085"
    />
    <script
      src="/_next/static/chunks/main-app.js?v=1720419547085"
      async=""
    ></script>
    <script src="/_next/static/chunks/app-pages-internals.js" async=""></script>
    <script src="/_next/static/chunks/app/login/page.js" async=""></script>
    <script src="/_next/static/chunks/app/layout.js" async=""></script>
    <title>Speak Fluent Dashboard</title>
    <meta name="description" content="Admin dashboard for Speak Fluent" />
    <!-- <link
      rel="icon"
      href="/icon.ico?194a30f75f42c42a"
      type="image/x-icon"
      sizes="32x32"
    /> -->
    <meta name="next-size-adjust" />
    <script src="/_next/static/chunks/polyfills.js" nomodule=""></script>
  </head>

  <body style="background: #ecf3f9">
    <style data-emotion="css-global 1u7jdk">
      :host,
      :root,
      [data-theme] {
        --chakra-ring-inset: var(--chakra-empty, /*!*/ /*!*/);
        --chakra-ring-offset-width: 0px;
        --chakra-ring-offset-color: #fff;
        --chakra-ring-color: rgba(66, 153, 225, 0.6);
        --chakra-ring-offset-shadow: 0 0 #0000;
        --chakra-ring-shadow: 0 0 #0000;
        --chakra-space-x-reverse: 0;
        --chakra-space-y-reverse: 0;
        --speak-fluent-colors-transparent: transparent;
        --speak-fluent-colors-current: currentColor;
        --speak-fluent-colors-black: #000000;
        --speak-fluent-colors-white: #ffffff;
        --speak-fluent-colors-whiteAlpha-50: rgba(255, 255, 255, 0.04);
        --speak-fluent-colors-whiteAlpha-100: rgba(255, 255, 255, 0.06);
        --speak-fluent-colors-whiteAlpha-200: rgba(255, 255, 255, 0.08);
        --speak-fluent-colors-whiteAlpha-300: rgba(255, 255, 255, 0.16);
        --speak-fluent-colors-whiteAlpha-400: rgba(255, 255, 255, 0.24);
        --speak-fluent-colors-whiteAlpha-500: rgba(255, 255, 255, 0.36);
        --speak-fluent-colors-whiteAlpha-600: rgba(255, 255, 255, 0.48);
        --speak-fluent-colors-whiteAlpha-700: rgba(255, 255, 255, 0.64);
        --speak-fluent-colors-whiteAlpha-800: rgba(255, 255, 255, 0.8);
        --speak-fluent-colors-whiteAlpha-900: rgba(255, 255, 255, 0.92);
        --speak-fluent-colors-blackAlpha-50: rgba(0, 0, 0, 0.04);
        --speak-fluent-colors-blackAlpha-100: rgba(0, 0, 0, 0.06);
        --speak-fluent-colors-blackAlpha-200: rgba(0, 0, 0, 0.08);
        --speak-fluent-colors-blackAlpha-300: rgba(0, 0, 0, 0.16);
        --speak-fluent-colors-blackAlpha-400: rgba(0, 0, 0, 0.24);
        --speak-fluent-colors-blackAlpha-500: rgba(0, 0, 0, 0.36);
        --speak-fluent-colors-blackAlpha-600: rgba(0, 0, 0, 0.48);
        --speak-fluent-colors-blackAlpha-700: rgba(0, 0, 0, 0.64);
        --speak-fluent-colors-blackAlpha-800: rgba(0, 0, 0, 0.8);
        --speak-fluent-colors-blackAlpha-900: rgba(0, 0, 0, 0.92);
        --speak-fluent-colors-gray-50: #e8e9eb;
        --speak-fluent-colors-gray-100: #a2a5ab;
        --speak-fluent-colors-gray-200: #7b8088;
        --speak-fluent-colors-gray-300: #424955;
        --speak-fluent-colors-gray-400: #3d4b5d;
        --speak-fluent-colors-gray-500: #1b2432;
        --speak-fluent-colors-gray-600: #131923;
        --speak-fluent-colors-gray-700: #10161f;
        --speak-fluent-colors-gray-800: #07111d;
        --speak-fluent-colors-gray-900: #050d16;
        --speak-fluent-colors-red-50: #fff5f5;
        --speak-fluent-colors-red-100: #fed7d7;
        --speak-fluent-colors-red-200: #feb2b2;
        --speak-fluent-colors-red-300: #fc8181;
        --speak-fluent-colors-red-400: #f56565;
        --speak-fluent-colors-red-500: #e53e3e;
        --speak-fluent-colors-red-600: #c53030;
        --speak-fluent-colors-red-700: #9b2c2c;
        --speak-fluent-colors-red-800: #822727;
        --speak-fluent-colors-red-900: #63171b;
        --speak-fluent-colors-orange-50: #fffaf0;
        --speak-fluent-colors-orange-100: #feebc8;
        --speak-fluent-colors-orange-200: #fbd38d;
        --speak-fluent-colors-orange-300: #f6ad55;
        --speak-fluent-colors-orange-400: #ed8936;
        --speak-fluent-colors-orange-500: #dd6b20;
        --speak-fluent-colors-orange-600: #c05621;
        --speak-fluent-colors-orange-700: #9c4221;
        --speak-fluent-colors-orange-800: #7b341e;
        --speak-fluent-colors-orange-900: #652b19;
        --speak-fluent-colors-yellow-50: #fffff0;
        --speak-fluent-colors-yellow-100: #fefcbf;
        --speak-fluent-colors-yellow-200: #faf089;
        --speak-fluent-colors-yellow-300: #f6e05e;
        --speak-fluent-colors-yellow-400: #ecc94b;
        --speak-fluent-colors-yellow-500: #d69e2e;
        --speak-fluent-colors-yellow-600: #b7791f;
        --speak-fluent-colors-yellow-700: #975a16;
        --speak-fluent-colors-yellow-800: #744210;
        --speak-fluent-colors-yellow-900: #5f370e;
        --speak-fluent-colors-green-50: #f0fff4;
        --speak-fluent-colors-green-100: #c6f6d5;
        --speak-fluent-colors-green-200: #9ae6b4;
        --speak-fluent-colors-green-300: #68d391;
        --speak-fluent-colors-green-400: #48bb78;
        --speak-fluent-colors-green-500: #38a169;
        --speak-fluent-colors-green-600: #2f855a;
        --speak-fluent-colors-green-700: #276749;
        --speak-fluent-colors-green-800: #22543d;
        --speak-fluent-colors-green-900: #1c4532;
        --speak-fluent-colors-teal-50: #e6fffa;
        --speak-fluent-colors-teal-100: #b2f5ea;
        --speak-fluent-colors-teal-200: #81e6d9;
        --speak-fluent-colors-teal-300: #4fd1c5;
        --speak-fluent-colors-teal-400: #38b2ac;
        --speak-fluent-colors-teal-500: #319795;
        --speak-fluent-colors-teal-600: #2c7a7b;
        --speak-fluent-colors-teal-700: #285e61;
        --speak-fluent-colors-teal-800: #234e52;
        --speak-fluent-colors-teal-900: #1d4044;
        --speak-fluent-colors-blue-50: #ebf8ff;
        --speak-fluent-colors-blue-100: #bee3f8;
        --speak-fluent-colors-blue-200: #90cdf4;
        --speak-fluent-colors-blue-300: #63b3ed;
        --speak-fluent-colors-blue-400: #4299e1;
        --speak-fluent-colors-blue-500: #3182ce;
        --speak-fluent-colors-blue-600: #2b6cb0;
        --speak-fluent-colors-blue-700: #2c5282;
        --speak-fluent-colors-blue-800: #2a4365;
        --speak-fluent-colors-blue-900: #1a365d;
        --speak-fluent-colors-cyan-50: #edfdfd;
        --speak-fluent-colors-cyan-100: #c4f1f9;
        --speak-fluent-colors-cyan-200: #9decf9;
        --speak-fluent-colors-cyan-300: #76e4f7;
        --speak-fluent-colors-cyan-400: #0bc5ea;
        --speak-fluent-colors-cyan-500: #00b5d8;
        --speak-fluent-colors-cyan-600: #00a3c4;
        --speak-fluent-colors-cyan-700: #0987a0;
        --speak-fluent-colors-cyan-800: #086f83;
        --speak-fluent-colors-cyan-900: #065666;
        --speak-fluent-colors-purple-50: #faf5ff;
        --speak-fluent-colors-purple-100: #e9d8fd;
        --speak-fluent-colors-purple-200: #d6bcfa;
        --speak-fluent-colors-purple-300: #b794f4;
        --speak-fluent-colors-purple-400: #9f7aea;
        --speak-fluent-colors-purple-500: #805ad5;
        --speak-fluent-colors-purple-600: #6b46c1;
        --speak-fluent-colors-purple-700: #553c9a;
        --speak-fluent-colors-purple-800: #44337a;
        --speak-fluent-colors-purple-900: #322659;
        --speak-fluent-colors-pink-50: #fff5f7;
        --speak-fluent-colors-pink-100: #fed7e2;
        --speak-fluent-colors-pink-200: #fbb6ce;
        --speak-fluent-colors-pink-300: #f687b3;
        --speak-fluent-colors-pink-400: #ed64a6;
        --speak-fluent-colors-pink-500: #d53f8c;
        --speak-fluent-colors-pink-600: #b83280;
        --speak-fluent-colors-pink-700: #97266d;
        --speak-fluent-colors-pink-800: #702459;
        --speak-fluent-colors-pink-900: #521b41;
        --speak-fluent-colors-linkedin-50: #e8f4f9;
        --speak-fluent-colors-linkedin-100: #cfedfb;
        --speak-fluent-colors-linkedin-200: #9bdaf3;
        --speak-fluent-colors-linkedin-300: #68c7ec;
        --speak-fluent-colors-linkedin-400: #34b3e4;
        --speak-fluent-colors-linkedin-500: #00a0dc;
        --speak-fluent-colors-linkedin-600: #008cc9;
        --speak-fluent-colors-linkedin-700: #0077b5;
        --speak-fluent-colors-linkedin-800: #005e93;
        --speak-fluent-colors-linkedin-900: #004471;
        --speak-fluent-colors-facebook-50: #e8f4f9;
        --speak-fluent-colors-facebook-100: #d9dee9;
        --speak-fluent-colors-facebook-200: #b7c2da;
        --speak-fluent-colors-facebook-300: #6482c0;
        --speak-fluent-colors-facebook-400: #4267b2;
        --speak-fluent-colors-facebook-500: #385898;
        --speak-fluent-colors-facebook-600: #314e89;
        --speak-fluent-colors-facebook-700: #29487d;
        --speak-fluent-colors-facebook-800: #223b67;
        --speak-fluent-colors-facebook-900: #1e355b;
        --speak-fluent-colors-messenger-50: #d0e6ff;
        --speak-fluent-colors-messenger-100: #b9daff;
        --speak-fluent-colors-messenger-200: #a2cdff;
        --speak-fluent-colors-messenger-300: #7ab8ff;
        --speak-fluent-colors-messenger-400: #2e90ff;
        --speak-fluent-colors-messenger-500: #0078ff;
        --speak-fluent-colors-messenger-600: #0063d1;
        --speak-fluent-colors-messenger-700: #0052ac;
        --speak-fluent-colors-messenger-800: #003c7e;
        --speak-fluent-colors-messenger-900: #002c5c;
        --speak-fluent-colors-whatsapp-50: #dffeec;
        --speak-fluent-colors-whatsapp-100: #b9f5d0;
        --speak-fluent-colors-whatsapp-200: #90edb3;
        --speak-fluent-colors-whatsapp-300: #65e495;
        --speak-fluent-colors-whatsapp-400: #3cdd78;
        --speak-fluent-colors-whatsapp-500: #22c35e;
        --speak-fluent-colors-whatsapp-600: #179848;
        --speak-fluent-colors-whatsapp-700: #0c6c33;
        --speak-fluent-colors-whatsapp-800: #01421c;
        --speak-fluent-colors-whatsapp-900: #001803;
        --speak-fluent-colors-twitter-50: #e5f4fd;
        --speak-fluent-colors-twitter-100: #c8e9fb;
        --speak-fluent-colors-twitter-200: #a8dcfa;
        --speak-fluent-colors-twitter-300: #83cdf7;
        --speak-fluent-colors-twitter-400: #57bbf5;
        --speak-fluent-colors-twitter-500: #1da1f2;
        --speak-fluent-colors-twitter-600: #1a94da;
        --speak-fluent-colors-twitter-700: #1681bf;
        --speak-fluent-colors-twitter-800: #136b9e;
        --speak-fluent-colors-twitter-900: #0d4d71;
        --speak-fluent-colors-telegram-50: #e3f2f9;
        --speak-fluent-colors-telegram-100: #c5e4f3;
        --speak-fluent-colors-telegram-200: #a2d4ec;
        --speak-fluent-colors-telegram-300: #7ac1e4;
        --speak-fluent-colors-telegram-400: #47a9da;
        --speak-fluent-colors-telegram-500: #0088cc;
        --speak-fluent-colors-telegram-600: #007ab8;
        --speak-fluent-colors-telegram-700: #006ba1;
        --speak-fluent-colors-telegram-800: #005885;
        --speak-fluent-colors-telegram-900: #003f5e;
        --speak-fluent-colors-primary-50: #e9f1f9;
        --speak-fluent-colors-primary-100: #81aedc;
        --speak-fluent-colors-primary-200: #4a8ccd;
        --speak-fluent-colors-primary-300: #993c5b;
        --speak-fluent-colors-primary-400: #35337a;
        --speak-fluent-colors-primary-500: #e97a5b;
        --speak-fluent-colors-primary-600: #e97a5b;
        --speak-fluent-colors-primary-700: #174777;
        --speak-fluent-colors-primary-800: #020031;
        --speak-fluent-colors-primary-900: #010025;
        --speak-fluent-colors-secondary-50: #e6eef2;
        --speak-fluent-colors-secondary-100: #b0cad5;
        --speak-fluent-colors-secondary-200: #8ab0c1;
        --speak-fluent-colors-secondary-300: #548ca5;
        --speak-fluent-colors-secondary-400: #337694;
        --speak-fluent-colors-secondary-500: #2f313f;
        --speak-fluent-colors-secondary-600: #004c6e;
        --speak-fluent-colors-secondary-700: #003c56;
        --speak-fluent-colors-secondary-800: #002e43;
        --speak-fluent-colors-secondary-900: #002333;
        --speak-fluent-colors-error-50: #fce6e6;
        --speak-fluent-colors-error-100: #f6b0b0;
        --speak-fluent-colors-error-200: #f18a8a;
        --speak-fluent-colors-error-300: #eb5454;
        --speak-fluent-colors-error-400: #e73333;
        --speak-fluent-colors-error-500: #e10000;
        --speak-fluent-colors-error-600: #cd0000;
        --speak-fluent-colors-error-700: #a00000;
        --speak-fluent-colors-error-800: #7c0000;
        --speak-fluent-colors-error-900: #5f0000;
        --speak-fluent-colors-success-50: #e7f7ea;
        --speak-fluent-colors-success-100: #b6e5bf;
        --speak-fluent-colors-success-200: #92d9a0;
        --speak-fluent-colors-success-300: #60c775;
        --speak-fluent-colors-success-400: #41bd5a;
        --speak-fluent-colors-success-500: #12ac31;
        --speak-fluent-colors-success-600: #109d2d;
        --speak-fluent-colors-success-700: #0d7a23;
        --speak-fluent-colors-success-800: #0a5f1b;
        --speak-fluent-colors-success-900: #084815;
        --speak-fluent-colors-warning-50: #fff6e6;
        --speak-fluent-colors-warning-100: #ffe3b0;
        --speak-fluent-colors-warning-200: #ffd68a;
        --speak-fluent-colors-warning-300: #ffc354;
        --speak-fluent-colors-warning-400: #ffb733;
        --speak-fluent-colors-warning-500: #ffa500;
        --speak-fluent-colors-warning-600: #e89600;
        --speak-fluent-colors-warning-700: #b57500;
        --speak-fluent-colors-warning-800: #8c5b00;
        --speak-fluent-colors-warning-900: #6b4500;
        --speak-fluent-colors-info-50: #f1f9fe;
        --speak-fluent-colors-info-100: #d3ecfd;
        --speak-fluent-colors-info-200: #bde3fb;
        --speak-fluent-colors-info-300: #9fd7fa;
        --speak-fluent-colors-info-400: #8dcff9;
        --speak-fluent-colors-info-500: #70c3f7;
        --speak-fluent-colors-info-600: #66b1e1;
        --speak-fluent-colors-info-700: #508aaf;
        --speak-fluent-colors-info-800: #3e6b88;
        --speak-fluent-colors-info-900: #2f5268;
        --speak-fluent-colors-kezaPrimary-50: #f1f9fe;
        --speak-fluent-colors-kezaPrimary-100: #d3ecfd;
        --speak-fluent-colors-kezaPrimary-200: #bde3fb;
        --speak-fluent-colors-kezaPrimary-300: #9fd7fa;
        --speak-fluent-colors-kezaPrimary-400: #8dcff9;
        --speak-fluent-colors-kezaPrimary-500: #aa4428;
        --speak-fluent-colors-kezaPrimary-600: #66b1e1;
        --speak-fluent-colors-kezaPrimary-700: #508aaf;
        --speak-fluent-colors-kezaPrimary-800: #3e6b88;
        --speak-fluent-colors-kezaPrimary-900: #2f5268;
        --speak-fluent-borders-none: 0;
        --speak-fluent-borders-1px: 1px solid;
        --speak-fluent-borders-2px: 2px solid;
        --speak-fluent-borders-4px: 4px solid;
        --speak-fluent-borders-8px: 8px solid;
        --speak-fluent-fonts-heading: var(--font-inter);
        --speak-fluent-fonts-body: var(--font-inter);
        --speak-fluent-fonts-mono:
          SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono',
          'Courier New', monospace;
        --speak-fluent-fontSizes-3xs: 0.45rem;
        --speak-fluent-fontSizes-2xs: 0.625rem;
        --speak-fluent-fontSizes-xs: 11px;
        --speak-fluent-fontSizes-sm: 13px;
        --speak-fluent-fontSizes-md: 15px;
        --speak-fluent-fontSizes-lg: 17px;
        --speak-fluent-fontSizes-xl: 22px;
        --speak-fluent-fontSizes-2xl: 26px;
        --speak-fluent-fontSizes-3xl: 30px;
        --speak-fluent-fontSizes-4xl: 34px;
        --speak-fluent-fontSizes-5xl: 38px;
        --speak-fluent-fontSizes-6xl: 50px;
        --speak-fluent-fontSizes-7xl: 70px;
        --speak-fluent-fontSizes-8xl: 6rem;
        --speak-fluent-fontSizes-9xl: 8rem;
        --speak-fluent-fontWeights-hairline: 100;
        --speak-fluent-fontWeights-thin: 200;
        --speak-fluent-fontWeights-light: 300;
        --speak-fluent-fontWeights-normal: 400;
        --speak-fluent-fontWeights-medium: 500;
        --speak-fluent-fontWeights-semibold: 600;
        --speak-fluent-fontWeights-bold: 700;
        --speak-fluent-fontWeights-extrabold: 800;
        --speak-fluent-fontWeights-black: 900;
        --speak-fluent-letterSpacings-tighter: -0.05em;
        --speak-fluent-letterSpacings-tight: -0.025em;
        --speak-fluent-letterSpacings-normal: 0;
        --speak-fluent-letterSpacings-wide: 0.025em;
        --speak-fluent-letterSpacings-wider: 0.05em;
        --speak-fluent-letterSpacings-widest: 0.1em;
        --speak-fluent-lineHeights-3: 0.75rem;
        --speak-fluent-lineHeights-4: 1rem;
        --speak-fluent-lineHeights-5: 1.25rem;
        --speak-fluent-lineHeights-6: 1.5rem;
        --speak-fluent-lineHeights-7: 1.75rem;
        --speak-fluent-lineHeights-8: 2rem;
        --speak-fluent-lineHeights-9: 2.25rem;
        --speak-fluent-lineHeights-10: 2.5rem;
        --speak-fluent-lineHeights-normal: normal;
        --speak-fluent-lineHeights-none: 1;
        --speak-fluent-lineHeights-shorter: 1.25;
        --speak-fluent-lineHeights-short: 1.375;
        --speak-fluent-lineHeights-base: 1.5;
        --speak-fluent-lineHeights-tall: 1.625;
        --speak-fluent-lineHeights-taller: 2;
        --speak-fluent-radii-none: 0;
        --speak-fluent-radii-sm: 0.125rem;
        --speak-fluent-radii-base: 0.25rem;
        --speak-fluent-radii-md: 0.375rem;
        --speak-fluent-radii-lg: 0.5rem;
        --speak-fluent-radii-xl: 0.75rem;
        --speak-fluent-radii-2xl: 1rem;
        --speak-fluent-radii-3xl: 1.5rem;
        --speak-fluent-radii-full: 9999px;
        --speak-fluent-space-1: 0.25rem;
        --speak-fluent-space-2: 0.5rem;
        --speak-fluent-space-3: 0.75rem;
        --speak-fluent-space-4: 1rem;
        --speak-fluent-space-5: 1.25rem;
        --speak-fluent-space-6: 1.5rem;
        --speak-fluent-space-7: 1.75rem;
        --speak-fluent-space-8: 2rem;
        --speak-fluent-space-9: 2.25rem;
        --speak-fluent-space-10: 2.5rem;
        --speak-fluent-space-12: 3rem;
        --speak-fluent-space-14: 3.5rem;
        --speak-fluent-space-16: 4rem;
        --speak-fluent-space-20: 5rem;
        --speak-fluent-space-24: 6rem;
        --speak-fluent-space-28: 7rem;
        --speak-fluent-space-32: 8rem;
        --speak-fluent-space-36: 9rem;
        --speak-fluent-space-40: 10rem;
        --speak-fluent-space-44: 11rem;
        --speak-fluent-space-48: 12rem;
        --speak-fluent-space-52: 13rem;
        --speak-fluent-space-56: 14rem;
        --speak-fluent-space-60: 15rem;
        --speak-fluent-space-64: 16rem;
        --speak-fluent-space-72: 18rem;
        --speak-fluent-space-80: 20rem;
        --speak-fluent-space-96: 24rem;
        --speak-fluent-space-px: 1px;
        --speak-fluent-space-0-5: 0.125rem;
        --speak-fluent-space-1-5: 0.375rem;
        --speak-fluent-space-2-5: 0.625rem;
        --speak-fluent-space-3-5: 0.875rem;
        --speak-fluent-shadows-xs: 0 0 0 1px rgba(0, 0, 0, 0.05);
        --speak-fluent-shadows-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --speak-fluent-shadows-base:
          0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        --speak-fluent-shadows-md:
          0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --speak-fluent-shadows-lg:
          0 10px 15px -3px rgba(0, 0, 0, 0.1),
          0 4px 6px -2px rgba(0, 0, 0, 0.05);
        --speak-fluent-shadows-xl:
          0 20px 25px -5px rgba(0, 0, 0, 0.1),
          0 10px 10px -5px rgba(0, 0, 0, 0.04);
        --speak-fluent-shadows-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        --speak-fluent-shadows-outline: 0 0 0 3px rgba(66, 153, 225, 0.6);
        --speak-fluent-shadows-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
        --speak-fluent-shadows-none: none;
        --speak-fluent-shadows-dark-lg:
          rgba(0, 0, 0, 0.1) 0px 0px 0px 1px, rgba(0, 0, 0, 0.2) 0px 5px 10px,
          rgba(0, 0, 0, 0.4) 0px 15px 40px;
        --speak-fluent-sizes-1: 0.25rem;
        --speak-fluent-sizes-2: 0.5rem;
        --speak-fluent-sizes-3: 0.75rem;
        --speak-fluent-sizes-4: 1rem;
        --speak-fluent-sizes-5: 1.25rem;
        --speak-fluent-sizes-6: 1.5rem;
        --speak-fluent-sizes-7: 1.75rem;
        --speak-fluent-sizes-8: 2rem;
        --speak-fluent-sizes-9: 2.25rem;
        --speak-fluent-sizes-10: 2.5rem;
        --speak-fluent-sizes-12: 3rem;
        --speak-fluent-sizes-14: 3.5rem;
        --speak-fluent-sizes-16: 4rem;
        --speak-fluent-sizes-20: 5rem;
        --speak-fluent-sizes-24: 6rem;
        --speak-fluent-sizes-28: 7rem;
        --speak-fluent-sizes-32: 8rem;
        --speak-fluent-sizes-36: 9rem;
        --speak-fluent-sizes-40: 10rem;
        --speak-fluent-sizes-44: 11rem;
        --speak-fluent-sizes-48: 12rem;
        --speak-fluent-sizes-52: 13rem;
        --speak-fluent-sizes-56: 14rem;
        --speak-fluent-sizes-60: 15rem;
        --speak-fluent-sizes-64: 16rem;
        --speak-fluent-sizes-72: 18rem;
        --speak-fluent-sizes-80: 20rem;
        --speak-fluent-sizes-96: 24rem;
        --speak-fluent-sizes-px: 1px;
        --speak-fluent-sizes-0-5: 0.125rem;
        --speak-fluent-sizes-1-5: 0.375rem;
        --speak-fluent-sizes-2-5: 0.625rem;
        --speak-fluent-sizes-3-5: 0.875rem;
        --speak-fluent-sizes-max: max-content;
        --speak-fluent-sizes-min: min-content;
        --speak-fluent-sizes-full: 100%;
        --speak-fluent-sizes-3xs: 14rem;
        --speak-fluent-sizes-2xs: 16rem;
        --speak-fluent-sizes-xs: 20rem;
        --speak-fluent-sizes-sm: 24rem;
        --speak-fluent-sizes-md: 28rem;
        --speak-fluent-sizes-lg: 32rem;
        --speak-fluent-sizes-xl: 36rem;
        --speak-fluent-sizes-2xl: 42rem;
        --speak-fluent-sizes-3xl: 48rem;
        --speak-fluent-sizes-4xl: 56rem;
        --speak-fluent-sizes-5xl: 64rem;
        --speak-fluent-sizes-6xl: 72rem;
        --speak-fluent-sizes-7xl: 80rem;
        --speak-fluent-sizes-8xl: 90rem;
        --speak-fluent-sizes-prose: 60ch;
        --speak-fluent-sizes-container-sm: 640px;
        --speak-fluent-sizes-container-md: 768px;
        --speak-fluent-sizes-container-lg: 1024px;
        --speak-fluent-sizes-container-xl: 1280px;
        --speak-fluent-zIndices-hide: -1;
        --speak-fluent-zIndices-auto: auto;
        --speak-fluent-zIndices-base: 0;
        --speak-fluent-zIndices-docked: 10;
        --speak-fluent-zIndices-dropdown: 1000;
        --speak-fluent-zIndices-sticky: 1100;
        --speak-fluent-zIndices-banner: 1200;
        --speak-fluent-zIndices-overlay: 1300;
        --speak-fluent-zIndices-modal: 1400;
        --speak-fluent-zIndices-popover: 1500;
        --speak-fluent-zIndices-skipLink: 1600;
        --speak-fluent-zIndices-toast: 1700;
        --speak-fluent-zIndices-tooltip: 1800;
        --speak-fluent-transition-property-common:
          background-color, border-color, color, fill, stroke, opacity,
          box-shadow, transform;
        --speak-fluent-transition-property-colors:
          background-color, border-color, color, fill, stroke;
        --speak-fluent-transition-property-dimensions: width, height;
        --speak-fluent-transition-property-position: left, right, top, bottom;
        --speak-fluent-transition-property-background:
          background-color, background-image, background-position;
        --speak-fluent-transition-easing-ease-in: cubic-bezier(0.4, 0, 1, 1);
        --speak-fluent-transition-easing-ease-out: cubic-bezier(0, 0, 0.2, 1);
        --speak-fluent-transition-easing-ease-in-out: cubic-bezier(
          0.4,
          0,
          0.2,
          1
        );
        --speak-fluent-transition-duration-ultra-fast: 50ms;
        --speak-fluent-transition-duration-faster: 100ms;
        --speak-fluent-transition-duration-fast: 150ms;
        --speak-fluent-transition-duration-normal: 200ms;
        --speak-fluent-transition-duration-slow: 300ms;
        --speak-fluent-transition-duration-slower: 400ms;
        --speak-fluent-transition-duration-ultra-slow: 500ms;
        --speak-fluent-blur-none: 0;
        --speak-fluent-blur-sm: 4px;
        --speak-fluent-blur-base: 8px;
        --speak-fluent-blur-md: 12px;
        --speak-fluent-blur-lg: 16px;
        --speak-fluent-blur-xl: 24px;
        --speak-fluent-blur-2xl: 40px;
        --speak-fluent-blur-3xl: 64px;
        --speak-fluent-breakpoints-base: 0em;
        --speak-fluent-breakpoints-sm: 30em;
        --speak-fluent-breakpoints-md: 48em;
        --speak-fluent-breakpoints-lg: 62em;
        --speak-fluent-breakpoints-xl: 80em;
        --speak-fluent-breakpoints-2xl: 96em;
      }

      .chakra-ui-light :host:not([data-theme]),
      .chakra-ui-light :root:not([data-theme]),
      .chakra-ui-light [data-theme]:not([data-theme]),
      [data-theme='light'] :host:not([data-theme]),
      [data-theme='light'] :root:not([data-theme]),
      [data-theme='light'] [data-theme]:not([data-theme]),
      :host[data-theme='light'],
      :root[data-theme='light'],
      [data-theme][data-theme='light'] {
        --speak-fluent-colors-chakra-body-text: var(
          --speak-fluent-colors-gray-800
        );
        --speak-fluent-colors-chakra-body-bg: var(--speak-fluent-colors-white);
        --speak-fluent-colors-chakra-border-color: var(
          --speak-fluent-colors-gray-200
        );
        --speak-fluent-colors-chakra-inverse-text: var(
          --speak-fluent-colors-white
        );
        --speak-fluent-colors-chakra-subtle-bg: var(
          --speak-fluent-colors-gray-100
        );
        --speak-fluent-colors-chakra-subtle-text: var(
          --speak-fluent-colors-gray-600
        );
        --speak-fluent-colors-chakra-placeholder-color: var(
          --speak-fluent-colors-gray-500
        );
      }

      .chakra-ui-dark :host:not([data-theme]),
      .chakra-ui-dark :root:not([data-theme]),
      .chakra-ui-dark [data-theme]:not([data-theme]),
      [data-theme='dark'] :host:not([data-theme]),
      [data-theme='dark'] :root:not([data-theme]),
      [data-theme='dark'] [data-theme]:not([data-theme]),
      :host[data-theme='dark'],
      :root[data-theme='dark'],
      [data-theme][data-theme='dark'] {
        --speak-fluent-colors-chakra-body-text: var(
          --speak-fluent-colors-whiteAlpha-900
        );
        --speak-fluent-colors-chakra-body-bg: var(
          --speak-fluent-colors-gray-800
        );
        --speak-fluent-colors-chakra-border-color: var(
          --speak-fluent-colors-whiteAlpha-300
        );
        --speak-fluent-colors-chakra-inverse-text: var(
          --speak-fluent-colors-gray-800
        );
        --speak-fluent-colors-chakra-subtle-bg: var(
          --speak-fluent-colors-gray-700
        );
        --speak-fluent-colors-chakra-subtle-text: var(
          --speak-fluent-colors-gray-400
        );
        --speak-fluent-colors-chakra-placeholder-color: var(
          --speak-fluent-colors-whiteAlpha-400
        );
      }
    </style>
    <style data-emotion="css-global fubdgu">
      html {
        line-height: 1.5;
        -webkit-text-size-adjust: 100%;
        font-family: system-ui, sans-serif;
        -webkit-font-smoothing: antialiased;
        text-rendering: optimizeLegibility;
        -moz-osx-font-smoothing: grayscale;
        touch-action: manipulation;
      }

      body {
        position: relative;
        min-height: 100%;
        margin: 0;
        font-feature-settings: 'kern';
      }

      :where(*, *::before, *::after) {
        border-width: 0;
        border-style: solid;
        box-sizing: border-box;
        word-wrap: break-word;
      }

      main {
        display: block;
      }

      hr {
        border-top-width: 1px;
        box-sizing: content-box;
        height: 0;
        overflow: visible;
      }

      :where(pre, code, kbd, samp) {
        font-family: SFMono-Regular, Menlo, Monaco, Consolas, monospace;
        font-size: 1em;
      }

      a {
        background-color: transparent;
        color: inherit;
        -webkit-text-decoration: inherit;
        text-decoration: inherit;
      }

      abbr[title] {
        border-bottom: none;
        -webkit-text-decoration: underline;
        text-decoration: underline;
        -webkit-text-decoration: underline dotted;
        -webkit-text-decoration: underline dotted;
        text-decoration: underline dotted;
      }

      :where(b, strong) {
        font-weight: bold;
      }

      small {
        font-size: 80%;
      }

      :where(sub, sup) {
        font-size: 75%;
        line-height: 0;
        position: relative;
        vertical-align: baseline;
      }

      sub {
        bottom: -0.25em;
      }

      sup {
        top: -0.5em;
      }

      img {
        border-style: none;
      }

      :where(button, input, optgroup, select, textarea) {
        font-family: inherit;
        font-size: 100%;
        line-height: 1.15;
        margin: 0;
      }

      :where(button, input) {
        overflow: visible;
      }

      :where(button, select) {
        text-transform: none;
      }

      :where(
        button::-moz-focus-inner,
        [type='button']::-moz-focus-inner,
        [type='reset']::-moz-focus-inner,
        [type='submit']::-moz-focus-inner
      ) {
        border-style: none;
        padding: 0;
      }

      fieldset {
        padding: 0.35em 0.75em 0.625em;
      }

      legend {
        box-sizing: border-box;
        color: inherit;
        display: table;
        max-width: 100%;
        padding: 0;
        white-space: normal;
      }

      progress {
        vertical-align: baseline;
      }

      textarea {
        overflow: auto;
      }

      :where([type='checkbox'], [type='radio']) {
        box-sizing: border-box;
        padding: 0;
      }

      input[type='number']::-webkit-inner-spin-button,
      input[type='number']::-webkit-outer-spin-button {
        -webkit-appearance: none !important;
      }

      input[type='number'] {
        -moz-appearance: textfield;
      }

      input[type='search'] {
        -webkit-appearance: textfield;
        outline-offset: -2px;
      }

      input[type='search']::-webkit-search-decoration {
        -webkit-appearance: none !important;
      }

      ::-webkit-file-upload-button {
        -webkit-appearance: button;
        font: inherit;
      }

      details {
        display: block;
      }

      summary {
        display: -webkit-box;
        display: -webkit-list-item;
        display: -ms-list-itembox;
        display: list-item;
      }

      template {
        display: none;
      }

      [hidden] {
        display: none !important;
      }

      :where(blockquote, dl, dd, h1, h2, h3, h4, h5, h6, hr, figure, p, pre) {
        margin: 0;
      }

      button {
        background: transparent;
        padding: 0;
      }

      fieldset {
        margin: 0;
        padding: 0;
      }

      :where(ol, ul) {
        margin: 0;
        padding: 0;
      }

      textarea {
        resize: vertical;
      }

      :where(button, [role='button']) {
        cursor: pointer;
      }

      button::-moz-focus-inner {
        border: 0 !important;
      }

      table {
        border-collapse: collapse;
      }

      :where(h1, h2, h3, h4, h5, h6) {
        font-size: inherit;
        font-weight: inherit;
      }

      :where(button, input, optgroup, select, textarea) {
        padding: 0;
        line-height: inherit;
        color: inherit;
      }

      :where(img, svg, video, canvas, audio, iframe, embed, object) {
        display: block;
      }

      :where(img, video) {
        max-width: 100%;
        height: auto;
      }

      [data-js-focus-visible]
        :focus:not([data-focus-visible-added]):not(
          [data-focus-visible-disabled]
        ) {
        outline: none;
        box-shadow: none;
      }

      select::-ms-expand {
        display: none;
      }

      :root,
      :host {
        --chakra-vh: 100vh;
      }

      @supports (height: -webkit-fill-available) {
        :root,
        :host {
          --chakra-vh: -webkit-fill-available;
        }
      }

      @supports (height: -moz-fill-available) {
        :root,
        :host {
          --chakra-vh: -moz-fill-available;
        }
      }

      @supports (height: 100dvh) {
        :root,
        :host {
          --chakra-vh: 100dvh;
        }
      }
    </style>
    <style data-emotion="css-global 1p3fb5g">
      body {
        font-family: var(--speak-fluent-fonts-body);
        color: var(--speak-fluent-colors-chakra-body-text);
        background: var(--speak-fluent-colors-chakra-body-bg);
        transition-property: background-color;
        transition-duration: var(--speak-fluent-transition-duration-normal);
        line-height: var(--speak-fluent-lineHeights-base);
      }

      *::-webkit-input-placeholder {
        color: var(--speak-fluent-colors-chakra-placeholder-color);
      }

      *::-moz-placeholder {
        color: var(--speak-fluent-colors-chakra-placeholder-color);
      }

      *:-ms-input-placeholder {
        color: var(--speak-fluent-colors-chakra-placeholder-color);
      }

      *::placeholder {
        color: var(--speak-fluent-colors-chakra-placeholder-color);
      }

      *,
      *::before,
      ::after {
        border-color: var(--speak-fluent-colors-chakra-border-color);
      }
    </style>
    <style data-emotion="css 1chirrj">
      .css-1chirrj {
        display: -webkit-box;
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
        -webkit-align-items: center;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        -webkit-justify-content: center;
        justify-content: center;
        height: 100vh;
        width: 100vw;
      }
    </style>
    <div class="css-1chirrj">
      <style data-emotion="css 8g8ihq">
        .css-8g8ihq {
          display: -webkit-box;
          display: -webkit-flex;
          display: -ms-flexbox;
          display: flex;
          -webkit-flex-direction: column;
          -ms-flex-direction: column;
          flex-direction: column;
          gap: 0.5rem;
        }
      </style>
      <div class="chakra-stack css-8g8ihq">
        <img
          alt="logo"
          loading="lazy"
          width="300"
          height="112"
          decoding="async"
          data-nimg="1"
          style="color: transparent; width: 100%; height: 100%"
          srcset="
            /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Flogo.aec81174.png&amp;w=384&amp;q=75 1x,
            /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Flogo.aec81174.png&amp;w=640&amp;q=75 2x
          "
          src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Flogo.aec81174.png&amp;w=640&amp;q=75"
        />
        <style data-emotion="css 1ahafqs">
          .css-1ahafqs {
            display: -webkit-inline-box;
            display: -webkit-inline-flex;
            display: -ms-inline-flexbox;
            display: inline-flex;
            -webkit-appearance: none;
            -moz-appearance: none;
            -ms-appearance: none;
            appearance: none;
            -webkit-align-items: center;
            -webkit-box-align: center;
            -ms-flex-align: center;
            align-items: center;
            -webkit-box-pack: center;
            -ms-flex-pack: center;
            -webkit-justify-content: center;
            justify-content: center;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            position: relative;
            white-space: nowrap;
            vertical-align: middle;
            outline: 2px solid transparent;
            outline-offset: 2px;
            line-height: 1.2;
            border-radius: 0.2rem;
            font-weight: 600;
            transition-property: var(--speak-fluent-transition-property-common);
            transition-duration: var(--speak-fluent-transition-duration-normal);
            variant: solid;
            font-family: var(--speak-fluent-fonts-body);
            -webkit-padding-start: var(--speak-fluent-space-6);
            padding-inline-start: var(--speak-fluent-space-6);
            -webkit-padding-end: var(--speak-fluent-space-6);
            padding-inline-end: var(--speak-fluent-space-6);
            min-height: 42px;
            background-color: var(--speak-fluent-colors-primary-500);
            padding: 0px;
            background: var(--speak-fluent-colors-black);
            cursor: pointer;
            color: var(--speak-fluent-colors-white);
            border: var(--speak-fluent-borders-none);
            height: 3rem;
            min-width: 13rem;
            font-size: 1rem;
          }

          .css-1ahafqs:focus-visible,
          .css-1ahafqs[data-focus-visible] {
            box-shadow: var(--speak-fluent-shadows-outline);
          }

          .css-1ahafqs:disabled,
          .css-1ahafqs[disabled],
          .css-1ahafqs[aria-disabled='true'],
          .css-1ahafqs[data-disabled] {
            opacity: 0.4;
            cursor: not-allowed;
            box-shadow: var(--speak-fluent-shadows-none);
          }

          .css-1ahafqs:active,
          .css-1ahafqs[data-active] {
            background: var(--speak-fluent-colors-primary-700);
          }

          @media screen and (min-width: 48em) {
            .css-1ahafqs {
              width: 85px;
            }
          }

          .css-1ahafqs:focus,
          .css-1ahafqs[data-focus] {
            background-color: primary.500;
          }

          .css-1ahafqs:hover,
          .css-1ahafqs[data-hover] {
            background: var(--speak-fluent-colors-black);
          }
        </style>
        <button type="button" class="chakra-button css-1ahafqs">
          <style data-emotion="css 1wh2kri">
            .css-1wh2kri {
              display: -webkit-inline-box;
              display: -webkit-inline-flex;
              display: -ms-inline-flexbox;
              display: inline-flex;
              -webkit-align-self: center;
              -ms-flex-item-align: center;
              align-self: center;
              -webkit-flex-shrink: 0;
              -ms-flex-negative: 0;
              flex-shrink: 0;
              -webkit-margin-end: 0.5rem;
              margin-inline-end: 0.5rem;
            }</style
          ><span class="chakra-button__icon css-1wh2kri"
            ><style data-emotion="css 123axov">
              .css-123axov {
                width: 1em;
                height: 1em;
                display: inline-block;
                line-height: 1em;
                -webkit-flex-shrink: 0;
                -ms-flex-negative: 0;
                flex-shrink: 0;
                color: currentColor;
                font-size: 2rem;
              }</style
            ><svg
              stroke="currentColor"
              fill="currentColor"
              stroke-width="0"
              version="1.1"
              x="0px"
              y="0px"
              viewBox="0 0 48 48"
              enable-background="new 0 0 48 48"
              focusable="false"
              class="chakra-icon css-123axov"
              aria-hidden="true"
              height="1em"
              width="1em"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fill="#FFC107"
                d="M43.611,20.083H42V20H24v8h11.303c-1.649,4.657-6.08,8-11.303,8c-6.627,0-12-5.373-12-12
	c0-6.627,5.373-12,12-12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C12.955,4,4,12.955,4,24
	c0,11.045,8.955,20,20,20c11.045,0,20-8.955,20-20C44,22.659,43.862,21.35,43.611,20.083z"
              ></path>
              <path
                fill="#FF3D00"
                d="M6.306,14.691l6.571,4.819C14.655,15.108,18.961,12,24,12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657
	C34.046,6.053,29.268,4,24,4C16.318,4,9.656,8.337,6.306,14.691z"
              ></path>
              <path
                fill="#4CAF50"
                d="M24,44c5.166,0,9.86-1.977,13.409-5.192l-6.19-5.238C29.211,35.091,26.715,36,24,36
	c-5.202,0-9.619-3.317-11.283-7.946l-6.522,5.025C9.505,39.556,16.227,44,24,44z"
              ></path>
              <path
                fill="#1976D2"
                d="M43.611,20.083H42V20H24v8h11.303c-0.792,2.237-2.231,4.166-4.087,5.571
	c0.001-0.001,0.002-0.001,0.003-0.002l6.19,5.238C36.971,39.205,44,34,44,24C44,22.659,43.862,21.35,43.611,20.083z"
              ></path></svg></span
          >Sign in with Google
        </button>
      </div>
    </div>
    <span></span><span id="__chakra_env" hidden=""></span>
    <script
      src="/_next/static/chunks/webpack.js?v=1720419547085"
      async=""
    ></script>
    <script>
      (self.__next_f = self.__next_f || []).push([0]);
      self.__next_f.push([2, null]);
    </script>
    <script>
      self.__next_f.push([
        1,
        '1:HL["/_next/static/media/c9a5bc6a7c948fb0-s.p.woff2","font",{"crossOrigin":"","type":"font/woff2"}]\n2:HL["/_next/static/css/app/layout.css?v=1720419547085","style"]\n0:"$L3"\n',
      ]);
    </script>
    <script>
      self.__next_f.push([
        1,
        '4:I["(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js",["app-pages-internals","static/chunks/app-pages-internals.js"],""]\n6:I["(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js",["app-pages-internals","static/chunks/app-pages-internals.js"],""]\n7:I["(app-pages-browser)/./src/app/login/page.tsx",["app/login/page","static/chunks/app/login/page.js"],""]\n8:I["(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js",["app-pages-internals","static/chunks/app-pages-internals.js"],""]\n9:I["(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js",["app-pages-internals","static/chunks/app-pages-internals.js"],""]\na:I["(app-pages-browser)/./src/providers/AppProvider.tsx",["app/layout","static/chunks/app/layout.js"],"AppProvider"]\nc:I["(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js",["app-pages-internals","static/chunks/app-pages-internals.js"],""]\nd:[]\n',
      ]);
    </script>
    <script>
      self.__next_f.push([
        1,
        '3:[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/app/layout.css?v=1720419547085","precedence":"next_static/css/app/layout.css","crossOrigin":"$undefined"}]],["$","$L4",null,{"buildId":"development","assetPrefix":"","initialCanonicalUrl":"/login?next=/api/quickbook","initialTree":["",{"children":["login",{"children":["__PAGE__?{\\"next\\":\\"/api/quickbook\\"}",{}]}]},"$undefined","$undefined",true],"initialSeedData":["",{"children":["login",{"children":["__PAGE__",{},["$L5",["$","$L6",null,{"propsForComponent":{"params":{},"searchParams":{"next":"/api/quickbook"}},"Component":"$7","isStaticGeneration":false}],null]]},["$","$L8",null,{"parallelRouterKey":"children","segmentPath":["children","login","children"],"loading":"$undefined","loadingStyles":"$undefined","loadingScripts":"$undefined","hasLoading":false,"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L9",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","notFoundStyles":"$undefined","styles":null}]]},[null,["$","html",null,{"className":"__className_aaf875","lang":"en","children":["$","body",null,{"style":{"background":"#ECF3F9"},"children":["$","$La",null,{"children":["$","$L8",null,{"parallelRouterKey":"children","segmentPath":["children"],"loading":"$undefined","loadingStyles":"$undefined","loadingScripts":"$undefined","hasLoading":false,"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L9",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\\"Segoe UI\\",Roboto,Helvetica,Arial,sans-serif,\\"Apple Color Emoji\\",\\"Segoe UI Emoji\\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":"404"}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],"notFoundStyles":[],"styles":null}]}]}]}],null]],"initialHead":[false,"$Lb"],"globalErrorComponent":"$c","missingSlots":"$Wd"}]]\n',
      ]);
    </script>
    <script>
      self.__next_f.push([
        1,
        'b:[["$","meta","0",{"name":"viewport","content":"width=device-width, initial-scale=1"}],["$","meta","1",{"charSet":"utf-8"}],["$","title","2",{"children":"Speak Fluent Dashboard"}],["$","meta","3",{"name":"description","content":"Admin dashboard for Speak Fluent"}],["$","link","4",{"rel":"icon","href":"/icon.ico?194a30f75f42c42a","type":"image/x-icon","sizes":"32x32"}],["$","meta","5",{"name":"next-size-adjust"}]]\n5:null\n',
      ]);
    </script>
    <script>
      self.__next_f.push([1, '']);
    </script>
  </body>
</html>
