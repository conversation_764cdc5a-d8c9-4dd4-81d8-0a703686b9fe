import { toaster } from '@/components/ui/toaster';
import { queryKey } from '@/constants/query-key';
import { ToastMessages } from '@/constants/toast-messages';
import { MutationConfig, useMutation, useQueryClient } from '@/lib/react-query';
import { useRouter } from 'next/navigation';

const sendEmail = async (body: any) => {
  const response = await fetch(`/api/send-email/gmail`, {
    method: 'POST',
    body: body,
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Error sending email');
  }
  return response.json();
};

type QueryFnType = typeof sendEmail;

export const useSendEmailMutation = (config?: MutationConfig<QueryFnType>) => {
  const queryClient = useQueryClient();
  const router = useRouter();

  return useMutation({
    onError: (err: any) => {
      console.log('err in email is ', err);
      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });

      // Check if this is the "no tokens" error and redirect
      if (err.message.includes('No tokens found')) {
        router.push('/profile?tab=account-settings');
      }
    },
    onSuccess: (res) => {
      toaster.create({
        type: 'success',
        description: res?.message || 'Email successfully sent',
      });
      queryClient.invalidateQueries([queryKey.emails.readEmails]);
    },
    retry: false,
    mutationKey: ['send-email'],
    mutationFn: sendEmail,
    ...config,
  });
};
