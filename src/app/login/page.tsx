// import Head from 'next/head';
import { createSupabaseServer } from '@/lib/supabase/server';
import Login from './Login';
import { Metadata } from 'next';
// import { getOrganizationName } from '@/utils/server-cookie-helper';

export async function generateMetadata(): Promise<Metadata> {
  // const organization_name = getOrganizationName();
  const fullTitle = `Soap - SLPs`;
  const description = `Soap Note platform.`;

  return {
    title: fullTitle,
    description,
  };
}

export default async function page() {
  const supabase = createSupabaseServer();
  const { data } = await supabase
    .from('patch_notes')
    .select('*')
    .order('created_at', { ascending: false })
    .limit(1)
    .single();
  // console.log('data is', { data, error });
  return <Login data={data} />;
}
