import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { env } from '@/constants/env';
import { tableNames } from '@/constants/table_names';
import { KlaviyoOAuth } from '@/lib/klaviyo/oauth';
import { KlaviyoDatabase } from '@/lib/klaviyo/db';

const supabaseAdmin = createClient(
  env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);
// const KLAVIYO_API_BASE = 'https://a.klaviyo.com/api';
// const CLIENT_ID = process.env.KLAVIYO_CLIENT_ID!;
// const CLIENT_SECRET = process.env.KLAVIYO_CLIENT_SECRET!;

export async function POST(request: Request) {
  const { userId } = await request.json();

  if (!userId) {
    return NextResponse.json(
      { error: 'Therapist ID required' },
      { status: 400 }
    );
  }

  try {
    const { data: klaviyoUser, error: fetchError } = await supabaseAdmin
      .from(tableNames.klaviyo_user)
      .select('*')
      .eq('user_id', userId)
      .single();

    if (fetchError || !klaviyoUser) {
      return NextResponse.json(
        { error: 'Therapist not found' },
        { status: 404 }
      );
    }

    if (klaviyoUser.access_token) {
      // Revoke tokens from Klaviyo
      await KlaviyoOAuth.revokeTokens(klaviyoUser.access_token);
      // Delete from database
      await KlaviyoDatabase.deleteIntegration(userId, supabaseAdmin);
    }

    return NextResponse.json({ message: 'Klaviyo account disconnected' });
  } catch (error) {
    console.error('Error disconnecting Klaviyo:', error);
    return NextResponse.json(
      { error: 'Failed to disconnect Klaviyo' },
      { status: 500 }
    );
  }
}
