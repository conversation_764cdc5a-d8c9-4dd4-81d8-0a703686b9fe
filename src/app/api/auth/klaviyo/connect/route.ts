import { env } from '@/constants/env';
import { KlaviyoOAuth } from '@/lib/klaviyo/oauth';
import { createClient } from '@supabase/supabase-js';
import { NextResponse } from 'next/server';

const supabaseAdmin = createClient(
  env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);
export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const userId = searchParams.get('userId');

  if (!userId) {
    return NextResponse.json(
      { error: 'Therapist ID required' },
      { status: 400 }
    );
  }

  try {
    const state = Buffer.from(
      JSON.stringify({
        userId,
        timestamp: Date.now(),
      })
    ).toString('base64');
    const authorizeUrl = await KlaviyoOAuth.generateAuthUrl(
      userId,
      state,
      supabaseAdmin
    );
    return NextResponse.json({ authorizeUrl });
  } catch (error) {
    console.error('Error initiating K<PERSON><PERSON><PERSON> OAuth:', error);
    return NextResponse.json(
      { error: 'Failed to initiate OAuth flow' },
      { status: 500 }
    );
  }
}
