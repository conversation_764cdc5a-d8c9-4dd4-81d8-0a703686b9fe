import { Email } from '@/app/(dashboard)/contacts/[id]/email/Email';
import {
  MenuContent,
  MenuItem,
  MenuRoot,
  MenuTrigger,
} from '@/components/ui/menu';
import { toaster } from '@/components/ui/toaster';
import { Badge, Box, Flex, Text } from '@chakra-ui/react';
import { FiEdit } from 'react-icons/fi';
import { HiOutlineDocumentText } from 'react-icons/hi';
import { IoLinkOutline } from 'react-icons/io5';
import { PiDotsThreeBold } from 'react-icons/pi';
import { RiDeleteBinLine } from 'react-icons/ri';

const FormCard = ({
  form,
  handleConfirmDeleteFormModal,
  getFormsViewState,
}: {
  form: any;
  handleConfirmDeleteFormModal: any;
  getFormsViewState: any;
}) => {
  // console.log('form in form card', form);
  const handleCopy = async () => {
    if (typeof window !== 'undefined') {
      try {
        await navigator.clipboard.writeText(
          `${window?.location.origin}/form/${form?.organization_slug}/${form?.slug}`
        );

        toaster.create({
          description: 'Copied Successfully',
          type: 'success',
        });
      } catch (err) {
        console.error('Failed to copy: ', err);
      }
    }
  };

  return (
    <Box
      onClick={() => getFormsViewState('edit', form?.id)}
      w={'full'}
      minH={'28'}
      border={'1px solid'}
      borderColor={'gray.50'}
      rounded={'12px'}
      px={{ base: '4', lg: '6' }}
      py={'3'}
      justifyContent={'center'}
      cursor={'pointer'}
      //onClick={item.onClick}
      _hover={{
        boxShadow: 'lg',
        transition: 'all 0.2s ease-in-out',
      }}
    >
      <Box width={'100%'}>
        <Flex gap={'4px'} justifyContent={'end'} alignItems={'center'}>
          {/* Dropdown Menu - Now properly separated */}
          <MenuRoot>
            <MenuTrigger asChild>
              <Box
                borderRadius="md"
                bg="#f2f2f2"
                _hover={{ bg: '#e0e0e0', color: '#000' }}
                cursor="pointer"
                color={'#000'}
                p={1}
                onClick={(e) => {
                  e.stopPropagation(); // Prevent triggering the parent click
                }}
              >
                <PiDotsThreeBold size={18} />
              </Box>
            </MenuTrigger>
            <MenuContent
              border={'1px solid'}
              borderColor={'gray.50'}
              rounded={'12px'}
              p={'2'}
              w={'100%'}
              boxShadow={'lg'}
              spaceY={'2'}
              onClick={(e) => e.stopPropagation()} // Add this to prevent parent click
            >
              <MenuItem
                cursor={'pointer'}
                value="edit"
                rounded={'8px'}
                _hover={{ bg: '#f2f2f2' }}
                px={'10px'}
                onClick={(e) => {
                  e.stopPropagation();
                  getFormsViewState('edit', form?.id);
                }}
              >
                <Box
                  w={'100%'}
                  textDecor="none"
                  display={'flex'}
                  alignItems={'center'}
                  gap={'10px'}
                  fontSize={'md'}
                  fontWeight={'500'}
                  color={'gray.700'}
                >
                  <FiEdit size={15} />
                  Edit
                </Box>
              </MenuItem>
              <MenuItem
                onClick={(e) => {
                  e.stopPropagation();
                  handleConfirmDeleteFormModal(form?.id);
                }}
                cursor={'pointer'}
                rounded={'8px'}
                _hover={{ bg: '#f2f2f2' }}
                value="delete"
                px={'10px'}
              >
                <Box
                  w={'100%'}
                  textDecor="none"
                  display={'flex'}
                  alignItems={'center'}
                  gap={'10px'}
                  fontSize={'md'}
                  fontWeight={'500'}
                  color={'gray.700'}
                >
                  <RiDeleteBinLine size={16} />
                  Delete
                </Box>
              </MenuItem>
              <MenuItem
                rounded={'8px'}
                _hover={{ bg: '#f2f2f2' }}
                cursor={'pointer'}
                value="view-answers"
                px={'10px'}
                onClick={(e) => {
                  e.stopPropagation();
                  getFormsViewState('view-answers', form?.id);
                }}
              >
                <Box
                  w={'100%'}
                  textDecor="none"
                  display={'flex'}
                  alignItems={'center'}
                  gap={'10px'}
                  fontSize={'md'}
                  fontWeight={'500'}
                  color={'gray.700'}
                >
                  <HiOutlineDocumentText size={18} />
                  View Answers
                </Box>
              </MenuItem>
              <Box
                cursor={'pointer'}
                fontSize={'md'}
                fontWeight={'500'}
                color={'gray.700'}
                rounded={'8px'}
                _hover={{ bg: '#f2f2f2' }}
                px={'12px'}
                py={'5px'}
                onClick={(e) => e.stopPropagation()} // Add this for the email item
              >
                <Email form={form} />
              </Box>
            </MenuContent>
          </MenuRoot>

          {/* Copy Link Button */}
          <Box
            title="Copy Form Link"
            p={1}
            borderRadius="md"
            bg="#f2f2f2"
            color={'#000'}
            _hover={{ bg: '#e0e0e0', color: '#000' }}
            cursor="pointer"
            pos={'relative'}
            onClick={(e) => {
              e.stopPropagation();
              handleCopy();
            }}
          >
            <IoLinkOutline size={18} />
          </Box>
        </Flex>
        <Box display={'flex'} flexDirection={'column'} gap={'5px'}>
          {/* Clickable area for editing */}
          <Text fontWeight={500} textTransform="capitalize" maxW={'70%'}>
            {form?.title}
          </Text>

          <Flex justifyContent={'space-between'} alignItems={'center'} mt={'2'}>
            <Badge px={'2'} rounded={'full'} bg={'#f2f2f2'} fontWeight={600}>
              {form?.questions?.length}{' '}
              {form?.questions?.length > 0 ? 'Questions' : 'Question'}
            </Badge>
          </Flex>
        </Box>
      </Box>
    </Box>
  );
};

export default FormCard;
