import { KLAVIYO_CONFIG } from './config';
import { KlaviyoOAuth, KlaviyoTokens } from './oauth';

export interface KlaviyoProfile {
  id?: string;
  email?: string;
  phone_number?: string;
  first_name?: string;
  last_name?: string;
  organization?: string;
  location?: {
    address1?: string;
    address2?: string;
    city?: string;
    country?: string;
    region?: string;
    zip?: string;
  };
  properties?: Record<string, any>;
}

export interface KlaviyoEvent {
  data: {
    type: 'event';
    attributes: {
      metric: {
        data: {
          type: 'metric';
          attributes: {
            name: string;
          };
        };
      };
      properties?: Record<string, any>;
      time?: string;
      value?: number;
      unique_id?: string;
    };
    relationships?: {
      profile?: {
        data: {
          type: 'profile';
          id?: string;
          attributes?: KlaviyoProfile;
        };
      };
    };
  };
}

export class KlaviyoAPI {
  private accessToken: string;
  private refreshToken?: string;
  private onTokenRefresh?: (tokens: KlaviyoTokens) => Promise<void>;

  constructor(
    accessToken: string,
    refreshToken?: string,
    onTokenRefresh?: (tokens: KlaviyoTokens) => Promise<void>
  ) {
    this.accessToken = accessToken;
    this.refreshToken = refreshToken;
    this.onTokenRefresh = onTokenRefresh;
  }

  /**
   * Make authenticated API request with automatic token refresh
   */
  private async makeRequest(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<Response> {
    const url = `${KLAVIYO_CONFIG.apiUrl}${endpoint}`;

    const response = await fetch(url, {
      ...options,
      headers: {
        Authorization: `Bearer ${this.accessToken}`,
        'Content-Type': 'application/json',
        Accept: 'application/json',
        revision: '2024-10-15', // Use latest API version
        ...options.headers,
      },
    });

    // Handle token refresh if access token is expired
    if (response.status === 401 && this.refreshToken && this.onTokenRefresh) {
      try {
        const newTokens = await KlaviyoOAuth.refreshAccessToken(
          this.refreshToken
        );
        this.accessToken = newTokens.access_token;
        this.refreshToken = newTokens.refresh_token;

        // Save new tokens
        await this.onTokenRefresh(newTokens);

        // Retry the request with new token
        return fetch(url, {
          ...options,
          headers: {
            Authorization: `Bearer ${this.accessToken}`,
            'Content-Type': 'application/json',
            Accept: 'application/json',
            revision: '2024-10-15',
            ...options.headers,
          },
        });
      } catch (error) {
        throw new Error('Failed to refresh token');
      }
    }

    return response;
  }

  /**
   * Get account information
   */
  async getAccount(): Promise<any> {
    const response = await this.makeRequest('/accounts/');
    if (!response.ok) {
      throw new Error(`Failed to get account: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data[0]; // First account
  }

  /**
   * Create or update a profile
   */
  async createProfile(profile: KlaviyoProfile): Promise<any> {
    const response = await this.makeRequest('/profiles/', {
      method: 'POST',
      body: JSON.stringify({
        data: {
          type: 'profile',
          attributes: profile,
        },
      }),
    });

    if (!response.ok) {
      throw new Error(`Failed to create profile: ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * Track an event
   */
  async trackEvent(event: KlaviyoEvent): Promise<any> {
    const response = await this.makeRequest('/events/', {
      method: 'POST',
      body: JSON.stringify(event),
    });

    if (!response.ok) {
      throw new Error(`Failed to track event: ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * Get lists
   */
  async getLists(): Promise<any> {
    const response = await this.makeRequest('/lists/');
    if (!response.ok) {
      throw new Error(`Failed to get lists: ${response.statusText}`);
    }
    return response.json();
  }

  /**
   * Create a list
   */
  async createList(name: string): Promise<any> {
    const response = await this.makeRequest('/lists/', {
      method: 'POST',
      body: JSON.stringify({
        data: {
          type: 'list',
          attributes: {
            name,
          },
        },
      }),
    });

    if (!response.ok) {
      throw new Error(`Failed to create list: ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * Add profile to list
   */
  async addProfileToList(listId: string, profileId: string): Promise<any> {
    const response = await this.makeRequest(
      `/lists/${listId}/relationships/profiles/`,
      {
        method: 'POST',
        body: JSON.stringify({
          data: [
            {
              type: 'profile',
              id: profileId,
            },
          ],
        }),
      }
    );

    if (!response.ok) {
      throw new Error(`Failed to add profile to list: ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * Get segments
   */
  async getSegments(): Promise<any> {
    const response = await this.makeRequest('/segments/');
    if (!response.ok) {
      throw new Error(`Failed to get segments: ${response.statusText}`);
    }
    return response.json();
  }

  /**
   * Get campaigns
   */
  async getCampaigns(): Promise<any> {
    const response = await this.makeRequest('/campaigns/');
    if (!response.ok) {
      throw new Error(`Failed to get campaigns: ${response.statusText}`);
    }
    return response.json();
  }

  /**
   * Get flows
   */
  async getFlows(): Promise<any> {
    const response = await this.makeRequest('/flows/');
    if (!response.ok) {
      throw new Error(`Failed to get flows: ${response.statusText}`);
    }
    return response.json();
  }
}
