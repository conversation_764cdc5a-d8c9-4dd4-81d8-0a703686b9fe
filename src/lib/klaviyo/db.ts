import { SupabaseClient } from '@supabase/supabase-js';
import { KlaviyoTokens } from './oauth';
import { tableNames } from '@/constants/table_names';
// import supabase from '../supabase/client';

export class KlaviyoDatabase {
  /**
   * Save Klaviyo integration for a user
   */
  static async saveIntegration(
    userId: string,
    tokens: KlaviyoTokens,
    accountInfo: any,
    supabase: SupabaseClient
  ): Promise<any> {
    //Verify user
    const { data: UserDetails, error: UserError } = await supabase
      .from(tableNames.users)
      .select('id')
      .eq('id', Number(userId))
      .maybeSingle();
    if (UserError) throw UserError;
    if (!UserDetails) throw new Error('User not found');

    console.log('tokens is ', tokens);

    //User Exist
    const expiresAt = new Date();
    expiresAt.setSeconds(expiresAt.getSeconds() + tokens.expires_in);
    await supabase.from(tableNames.klaviyo_user).insert({
      user_id: Number(userId),
      access_token: tokens.access_token,
      refresh_token: tokens.refresh_token,
      account_name:
        accountInfo.attributes?.contact_information?.organization_name,
      account_id: accountInfo.id,
      expires_at: expiresAt,
    });
  }

  /**
   * Get Klaviyo integration for a user
   */
  static async getIntegration(
    userId: string,
    supabase: SupabaseClient
  ): Promise<any> {
    return supabase
      .from(tableNames.klaviyo_user)
      .select('*')
      .eq('user_id', Number(userId))
      .maybeSingle();
  }

  /**
   * Update tokens after refresh
   */
  static async updateTokens(
    userId: string,
    tokens: KlaviyoTokens,
    supabase: SupabaseClient
  ): Promise<any> {
    const expiresAt = new Date();
    expiresAt.setSeconds(expiresAt.getSeconds() + tokens.expires_in);

    return await supabase
      .from(tableNames.klaviyo_user)
      .update({
        access_token: tokens.access_token,
        refresh_token: tokens.refresh_token,
        updatedAt: new Date(),
        expires_at: expiresAt,
      })
      .select('*');
  }

  /**
   * Delete integration (when user disconnects)
   */
  static async deleteIntegration(
    userId: string,
    supabase: SupabaseClient
  ): Promise<void> {
    await supabase
      .from(tableNames.klaviyo_user)
      .delete()
      .eq('user_id', Number(userId));
  }
}
