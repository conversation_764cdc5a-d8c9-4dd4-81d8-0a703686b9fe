import { SupabaseClient } from '@supabase/supabase-js';
import { KLAVIYO_CONFIG } from './config';
import crypto, { randomBytes, createHash } from 'crypto';
import { tableNames } from '@/constants/table_names';

export interface KlaviyoTokens {
  access_token: string;
  refresh_token: string;
  token_type: 'Bearer';
  expires_in: number;
  scope: string;
}

export interface KlaviyoAccount {
  id: string;
  company_name: string;
  contact_email: string;
  industry: string;
  timezone: string;
  currency: string;
}

export class KlaviyoOAuth {
  /**
   * Generate OAuth authorization URL
   */

  // Utility to generate Base64-encoded Authorization header
  static generateBasicAuthHeader(
    clientId: string,
    clientSecret: string
  ): string {
    const credentials = `${clientId}:${clientSecret}`;
    const base64Credentials = Buffer.from(credentials).toString('base64');

    return `Basic ${base64Credentials}`;
  }

  // Generate PKCE code verifier and challenge
  static generatePkceCodes() {
    const codeVerifier = randomBytes(32).toString('base64url');
    const codeChallenge = createHash('sha256')
      .update(codeVerifier)
      .digest('base64url');
    return { codeVerifier, codeChallenge };
  }
  static async generateAuthUrl(
    userId: string,
    state?: string,
    supabase?: SupabaseClient
  ): Promise<string> {
    const { codeChallenge, codeVerifier } = this.generatePkceCodes();

    // Store codeVerifier in Supabase
    if (supabase) {
      const { error } = await supabase
        .from(tableNames.users)
        .update({ klaviyo_code_verifier: codeVerifier })
        .eq('id', Number(userId));
      if (error) {
        throw new Error(`Failed to store code verifier: ${error.message}`);
      }
    }

    const params = new URLSearchParams({
      response_type: 'code',
      client_id: KLAVIYO_CONFIG.clientId,
      redirect_uri: KLAVIYO_CONFIG.redirectUri,
      scope: KLAVIYO_CONFIG.scopes.join(' '),
      state: state || crypto.randomBytes(16).toString('hex'),
      code_challenge: codeChallenge,
      code_challenge_method: 'S256',
    });

    return `${KLAVIYO_CONFIG.authUrl}?${params.toString()}`;
  }

  /**
   * Exchange authorization code for access tokens
   */
  static async exchangeCodeForTokens(
    code: string,
    userId: string,
    supabase?: SupabaseClient
  ): Promise<KlaviyoTokens> {
    let code_verifier = '';
    // Store codeVerifier in Supabase
    if (supabase) {
      const { data, error } = await supabase
        .from(tableNames.users)
        .select('klaviyo_code_verifier')
        .eq('id', Number(userId))
        .maybeSingle();
      if (error) {
        throw new Error(`Failed to store code verifier: ${error.message}`);
      }
      code_verifier = data?.klaviyo_code_verifier;
    }
    const authHeader = this.generateBasicAuthHeader(
      KLAVIYO_CONFIG.clientId,
      KLAVIYO_CONFIG.clientSecret
    );
    // console.log('payload is ', {
    //   grant_type: 'authorization_code',
    //   code,
    //   redirect_uri: KLAVIYO_CONFIG.redirectUri,
    //   client_id: KLAVIYO_CONFIG.clientId,
    //   client_secret: KLAVIYO_CONFIG.clientSecret,
    //   code_verifier,
    // });

    const response = await fetch(KLAVIYO_CONFIG.tokenUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        Authorization: authHeader,
      },
      body: new URLSearchParams({
        grant_type: 'authorization_code',
        code,
        redirect_uri: KLAVIYO_CONFIG.redirectUri,
        client_id: KLAVIYO_CONFIG.clientId,
        client_secret: KLAVIYO_CONFIG.clientSecret,
        code_verifier,
      }),
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to exchange code for tokens: ${error}`);
    }

    return response.json();
  }

  /**
   * Refresh access token using refresh token
   */
  static async refreshAccessToken(
    refreshToken: string
  ): Promise<KlaviyoTokens> {
    const response = await fetch(KLAVIYO_CONFIG.tokenUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        grant_type: 'refresh_token',
        refresh_token: refreshToken,
        client_id: KLAVIYO_CONFIG.clientId,
        client_secret: KLAVIYO_CONFIG.clientSecret,
      }),
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to refresh token: ${error}`);
    }

    return response.json();
  }

  /**
   * Revoke access and refresh tokens
   */
  static async revokeTokens(token: string): Promise<void> {
    const authHeader = this.generateBasicAuthHeader(
      KLAVIYO_CONFIG.clientId,
      KLAVIYO_CONFIG.clientSecret
    );
    const response = await fetch(`${KLAVIYO_CONFIG.baseUrl}/oauth/revoke`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        Authorization: authHeader,
      },
      body: new URLSearchParams({
        token,
        client_id: KLAVIYO_CONFIG.clientId,
        client_secret: KLAVIYO_CONFIG.clientSecret,
      }),
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to revoke tokens: ${error}`);
    }
  }
}
