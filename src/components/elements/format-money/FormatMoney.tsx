import { currencyOptions } from '@/data/options';

type OptionType = {
  currencyCode?: string | null | undefined;
  showCurrencyCode?: boolean;
  showSymbol?: boolean;
};

export const formatMoney = (
  amount: number | string | null | undefined,
  options?: OptionType
): string => {
  const currencyCode = options?.currencyCode;
  const showCurrencyCode = options?.showCurrencyCode ?? true;
  const showSymbol = options?.showSymbol ?? true;

  console.log('options in formatMoney:', options);
  console.log('currencyCode in formatMoney:', currencyCode);

  // Get currency code from org
  let currency = currencyCode; // fallback

  try {
    const raw = localStorage.getItem('UserState');
    const dataOrg = raw ? JSON.parse(raw) : null;
    const org = dataOrg?.UserState?.organization;
    currency = currencyCode || org?.currency_code || 'CAD';
  } catch (error) {
    currency = currencyCode || 'CAD'; // fallback
  }

  // Format the amount
  const formatAmount = (value: number | string | null | undefined): string => {
    if (!value && value !== 0) return '0.00';

    const numValue = typeof value === 'string' ? parseFloat(value) : value;

    if (isNaN(numValue)) return '0.00';

    // Format with commas and 2 decimal places
    return numValue.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
  };

  const formattedAmount = formatAmount(amount);

  // Find currency symbol
  const currencyOption = currencyOptions.find(
    (option) => option.value === currency
  );
  const symbol = currencyOption?.symbol || '$';

  // Return formatted string: $12,345.00 CAD
  const currencyCodeString = showCurrencyCode ? ` ${currency}` : '';
  const symbolString = showSymbol ? `${symbol}` : '';
  return `${symbolString}${formattedAmount}${currencyCodeString}`;
};
